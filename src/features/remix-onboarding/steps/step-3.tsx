'use client';

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { useRemixOnboarding, validateStep3 } from '../remix-onboarding-context';

export default function Step3() {
  const { data, updateData, nextStep, prevStep } = useRemixOnboarding();
  const [bio, setBio] = useState(data.bio);
  const [location, setLocation] = useState(data.location);
  const [phoneNumber, setPhoneNumber] = useState(data.phoneNumber || '');
  const [phoneError, setPhoneError] = useState('');

  const validatePhoneNumber = (phone: string): string => {
    if (!phone.trim()) return 'Phone number is required';

    const cleanPhone = phone.replace(/\D/g, '');
    if (cleanPhone.length < 10) return 'Phone number must be at least 10 digits';
    if (cleanPhone.length > 15) return 'Phone number must be no more than 15 digits';

    return '';
  };

  const formatPhoneNumber = (value: string): string => {
    // Remove all non-digit characters
    const cleanValue = value.replace(/\D/g, '');

    // Limit to 15 digits (international standard)
    const limitedValue = cleanValue.slice(0, 15);

    // Format US numbers (10 digits) as (XXX) XXX-XXXX
    if (limitedValue.length === 10) {
      return `(${limitedValue.slice(0, 3)}) ${limitedValue.slice(3, 6)}-${limitedValue.slice(6)}`;
    }

    // For other lengths, just add spaces for readability
    if (limitedValue.length > 10) {
      return limitedValue.replace(/(\d{3})(\d{3})(\d{4})(.*)/, '$1 $2 $3 $4').trim();
    }

    // For partial numbers, format progressively
    if (limitedValue.length >= 6) {
      return `(${limitedValue.slice(0, 3)}) ${limitedValue.slice(3, 6)}-${limitedValue.slice(6)}`;
    } else if (limitedValue.length >= 3) {
      return `(${limitedValue.slice(0, 3)}) ${limitedValue.slice(3)}`;
    }

    return limitedValue;
  };

  const handlePhoneChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const formattedValue = formatPhoneNumber(e.target.value);
    setPhoneNumber(formattedValue);

    // Clear error when user starts typing
    if (phoneError) {
      setPhoneError('');
    }
  };

  const handlePhoneBlur = () => {
    const error = validatePhoneNumber(phoneNumber);
    setPhoneError(error);
  };

  const handleContinue = () => {
    const updatedData = { bio, location, phoneNumber };
    updateData(updatedData);

    if (validateStep3({ ...data, ...updatedData })) {
      nextStep();
    }
  };

  const handleBack = () => {
    prevStep();
  };

  const isValid = bio.trim().length > 0 && location.trim().length > 0 && phoneNumber.trim().length > 0 && !phoneError;

  return (
    <div className="space-y-6">
      {/* Title */}
      <div className="flex flex-col justify-start items-start">
        <h1 className="max-w-[328.81px] flex justify-center text-foreground text-3xl font-bold font-arvo leading-10">
          Describe yourself
        </h1>
      </div>

      {/* Form Fields */}
      <div className="space-y-6">
        {/* Bio Field */}
        <div className="space-y-2">
          <Label
            htmlFor="bio"
            className="text-sm font-arvo text-muted-foreground"
          >
            Write a short bio best describing you *
          </Label>
          <Textarea
            id="bio"
            value={bio}
            onChange={(e) => {
              if (e.target.value.length <= 1000) {
                setBio(e.target.value);
              }
            }}
            placeholder="e.g. I am an enthusiastic song writer"
            rows={4}
            maxLength={1000}
            className="w-full px-4 py-3.5 bg-card border border-border rounded text-sm font-lato text-card-foreground placeholder:text-muted-foreground focus:border-primary focus:ring-1 focus:ring-primary resize-none"
          />
          <p className="text-xs text-muted-foreground font-lato">
            {bio.length}/1000 characters
          </p>
        </div>

        {/* Location Field */}
        <div className="space-y-2">
          <Label
            htmlFor="location"
            className="text-sm font-arvo text-muted-foreground"
          >
            What&apos;s your location? *
          </Label>
          <Input
            id="location"
            type="text"
            value={location}
            onChange={(e) => setLocation(e.target.value)}
            placeholder="e.g. Los Angeles"
            className="w-full px-4 py-3 bg-card border border-border rounded text-sm font-lato text-card-foreground placeholder:text-muted-foreground focus:border-primary focus:ring-1 focus:ring-primary"
          />
        </div>

        {/* Phone Number Field */}
        <div className="flex flex-col justify-start items-start gap-1.5">
          <Label
            htmlFor="phone-number"
            className="text-sm font-arvo text-muted-foreground"
          >
            What&apos;s your phone number? *
          </Label>
          <Input
            id="phone-number"
            type="tel"
            value={phoneNumber}
            onChange={handlePhoneChange}
            onBlur={handlePhoneBlur}
            placeholder="e.g. (*************"
            className={`w-full px-4 py-2.5 bg-card border rounded text-sm font-lato text-card-foreground placeholder:text-muted-foreground focus:ring-1 ${
              phoneError
                ? 'border-destructive focus:border-destructive focus:ring-destructive'
                : 'border-border focus:border-primary focus:ring-primary'
            }`}
          />
          {phoneError && (
            <p className="text-xs text-destructive font-lato">
              {phoneError}
            </p>
          )}
        </div>
      </div>

      {/* Navigation Buttons */}
      <div className="flex justify-between items-center pt-6">
        <Button
          onClick={handleBack}
          variant="outline"
          className="px-6 py-3 font-arvo font-bold text-base border-muted-foreground text-muted-foreground hover:bg-muted"
        >
          Back
        </Button>
        <Button
          onClick={handleContinue}
          disabled={!isValid}
          className="px-6 py-3 bg-primary text-primary-foreground font-arvo font-bold text-base hover:bg-primary/90 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          Continue
        </Button>
      </div>
    </div>
  );
}
