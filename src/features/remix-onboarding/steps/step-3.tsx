'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { useRemixOnboarding, validateStep3 } from '../remix-onboarding-context';

export default function Step3() {
  const { data, updateData, nextStep, prevStep } = useRemixOnboarding();
  const [bio, setBio] = useState(data.bio);
  const [location, setLocation] = useState(data.location);

  const handleContinue = () => {
    const updatedData = { bio, location };
    updateData(updatedData);
    
    if (validateStep3({ ...data, ...updatedData })) {
      nextStep();
    }
  };

  const handleBack = () => {
    prevStep();
  };

  const isValid = bio.trim().length > 0 && location.trim().length > 0;

  return (
    <div className="space-y-6">
      {/* Title */}
      <div className="text-center">
        <h1 className="text-3xl font-bold text-foreground font-arvo leading-tight">
          Describe yourself
        </h1>
      </div>

      {/* Form Fields */}
      <div className="space-y-6">
        {/* Bio Field */}
        <div className="space-y-2">
          <Label
            htmlFor="bio"
            className="text-sm font-arvo text-muted-foreground"
          >
            Write a short bio best describing you <span className="text-red-500">*</span>
          </Label>
          <Textarea
            id="bio"
            value={bio}
            onChange={(e) => {
              if (e.target.value.length <= 1000) {
                setBio(e.target.value);
              }
            }}
            placeholder="e.g. I am an enthusiastic song writer"
            rows={4}
            maxLength={1000}
            className="w-full px-4 py-3.5 bg-card border border-border rounded text-sm font-lato text-card-foreground placeholder:text-muted-foreground focus:border-primary focus:ring-1 focus:ring-primary resize-none"
          />
          <p className="text-xs text-muted-foreground font-lato">
            {bio.length}/1000 characters
          </p>
        </div>

        {/* Location Field */}
        <div className="space-y-2">
          <Label
            htmlFor="location"
            className="text-sm font-arvo text-muted-foreground"
          >
            What&apos;s your location?*
          </Label>
          <Input
            id="location"
            type="text"
            value={location}
            onChange={(e) => setLocation(e.target.value)}
            placeholder="e.g. Los Angeles"
            className="w-full px-4 py-3 bg-card border border-border rounded text-sm font-lato text-card-foreground placeholder:text-muted-foreground focus:border-primary focus:ring-1 focus:ring-primary"
          />
        </div>
      </div>

      {/* Navigation Buttons */}
      <div className="flex justify-between items-center pt-6">
        <Button
          onClick={handleBack}
          variant="outline"
          className="px-6 py-3 font-arvo font-bold text-base border-muted-foreground text-muted-foreground hover:bg-muted"
        >
          Back
        </Button>
        <Button
          onClick={handleContinue}
          disabled={!isValid}
          className="px-6 py-3 bg-primary text-primary-foreground font-arvo font-bold text-base hover:bg-primary/90 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          Continue
        </Button>
      </div>
    </div>
  );
}
