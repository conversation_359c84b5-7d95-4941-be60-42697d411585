'use client';

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { ChevronDown } from 'lucide-react';
import { useRemixOnboarding, validateStep4 } from '../remix-onboarding-context';

type InitialRoles = {
  songwriter: boolean;
  toplineWriter: boolean;
  lyricist: boolean;
  melodywriter: boolean;
  beatmaker: boolean;
  arranger: boolean;
  remixer: boolean;
  vocalist: boolean;
  musician: boolean;
  engineer: boolean;
  artist: boolean;
};

type RoleKey = keyof InitialRoles;

interface RoleGroup {
  id: string;
  title: string;
  roles: Array<{
    key: Role<PERSON><PERSON>;
    label: string;
  }>;
}

const roleGroups: RoleGroup[] = [
  {
    id: 'songwriter',
    title: 'Songwriter/Composer',
    roles: [
      { key: 'songwriter', label: 'Songwriter' },
      { key: 'toplineWriter', label: 'Topline Writer' },
      { key: 'lyricist', label: 'Lyricist' },
      { key: 'melodywriter', label: 'Melodywriter' },
      { key: 'beatmaker', label: 'Beatmaker/Trackmaker' },
      { key: 'arranger', label: 'Arranger' },
      { key: 'remixer', label: 'Remixer' },
    ],
  },
  {
    id: 'musician',
    title: 'Music/Instrumentalist',
    roles: [
      { key: 'musician', label: 'Musician/Instrumentalist' },
    ],
  },
  {
    id: 'engineer',
    title: 'Engineer/Editor',
    roles: [
      { key: 'engineer', label: 'Engineer/Editor' },
    ],
  },
  {
    id: 'artist',
    title: 'Artist/Performer',
    roles: [
      { key: 'artist', label: 'Artist/Performer' },
    ],
  },
  {
    id: 'vocalist',
    title: 'Vocalist',
    roles: [
      { key: 'vocalist', label: 'Vocalist' },
    ],
  },
];

export default function Step4() {
  const { data, updateData, nextStep, prevStep } = useRemixOnboarding();
  const [roles, setRoles] = useState(data.roles);
  const [expandedGroups, setExpandedGroups] = useState<string[]>(['songwriter']);

  const toggleGroup = (groupId: string) => {
    setExpandedGroups(prev =>
      prev.includes(groupId)
        ? prev.filter(id => id !== groupId)
        : [...prev, groupId]
    );
  };

  const handleRoleChange = (roleKey: RoleKey, checked: boolean) => {
    const updatedRoles = { ...roles, [roleKey]: checked };
    setRoles(updatedRoles);
    updateData({ roles: updatedRoles });
  };

  const handleContinue = () => {
    if (validateStep4({ ...data, roles })) {
      nextStep();
    }
  };

  const handleBack = () => {
    prevStep();
  };

  const isValid = Object.values(roles).some(role => role);

  return (
    <div className="space-y-6">
      {/* Title */}
        <h1 className="text-foreground text-3xl font-bold font-arvo leading-tight">
          Pick roles that fit you
        </h1>

      {/* Subtitle */}
        <p className="text-sm text-foreground font-arvo">
          Select roles that match your vibe (at least one required)
        </p>
     

      {/* Role Groups */}
      <div className="space-y-4">
        {roleGroups.map((group) => {
          const isExpanded = expandedGroups.includes(group.id);
          const hasSelectedRoles = group.roles.some(role => roles[role.key]);

          return (
            <div
              key={group.id}
              className="border border-border rounded-lg bg-card/60 overflow-hidden"
            >
              {/* Group Header */}
              <button
                onClick={() => toggleGroup(group.id)}
                className="w-full flex items-center justify-between p-4 text-left hover:bg-muted/50 transition-colors"
              >
                <div className="flex items-center gap-3">
                  <Checkbox
                    checked={hasSelectedRoles}
                    disabled
                    className="w-5 h-5 border-primary data-[state=checked]:bg-primary data-[state=checked]:border-primary data-[state=checked]:text-primary-foreground"
                  />
                  <span className="text-base font-semibold text-card-foreground font-lato">
                    {group.title}
                  </span>
                </div>
                <ChevronDown
                  className={`w-5 h-5 text-muted-foreground transition-transform ${
                    isExpanded ? 'rotate-180' : ''
                  }`}
                />
              </button>

              {/* Group Content */}
              {isExpanded && (
                <div className="px-4 pb-4 space-y-1.5">
                  {group.roles.map((role) => (
                    <div key={role.key} className="flex items-center gap-3 py-1">
                      <Checkbox
                        id={role.key}
                        checked={roles[role.key]}
                        onCheckedChange={(checked) => handleRoleChange(role.key, checked as boolean)}
                        className="w-5 h-5 border-primary data-[state=checked]:bg-primary data-[state=checked]:border-primary data-[state=checked]:text-primary-foreground"
                      />
                      <Label
                        htmlFor={role.key}
                        className="text-sm text-card-foreground font-inter cursor-pointer"
                      >
                        {role.label}
                      </Label>
                    </div>
                  ))}
                </div>
              )}
            </div>
          );
        })}
      </div>

      {/* Navigation Buttons */}
      <div className="flex justify-between items-center pt-6">
        <Button
          onClick={handleBack}
          variant="outline"
          className="px-6 py-3 font-arvo font-bold text-base border-muted text-muted-foreground hover:bg-muted"
        >
          Back
        </Button>
        <Button
          onClick={handleContinue}
          disabled={!isValid}
          className="px-6 py-3 bg-primary text-primary-foreground font-arvo font-bold text-base hover:bg-primary/90 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          Continue
        </Button>
      </div>
    </div>
  );
}
