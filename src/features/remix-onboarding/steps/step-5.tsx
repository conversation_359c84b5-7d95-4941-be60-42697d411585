'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useRemixOnboarding } from '../remix-onboarding-context';
import { Loader2 } from 'lucide-react';

const socialFields = [
  {
    key: 'spotify' as const,
    label: 'Spotify',
    placeholder: 'Paste your Spotify profile link',
  },
  {
    key: 'appleMusic' as const,
    label: 'Apple Music',
    placeholder: 'Paste your Apple Music profile link',
  },
  {
    key: 'youtube' as const,
    label: 'YouTube',
    placeholder: 'Paste your YouTube profile link',
  },
  {
    key: 'instagram' as const,
    label: 'Instagram',
    placeholder: 'Paste your Instagram profile link',
  },
  {
    key: 'linkedin' as const,
    label: 'Linkedin',
    placeholder: 'Paste your Linkedin profile link',
  },
  {
    key: 'website' as const,
    label: 'Website',
    placeholder: 'Paste other social profile links(if any)',
  },
];

export default function Step5() {
  const { data, updateData, prevStep, submitOnboarding, isLoading } = useRemixOnboarding();
  const [socialLinks, setSocialLinks] = useState(data.socialLinks);

  const handleInputChange = (key: keyof typeof socialLinks, value: string) => {
    const updatedLinks = { ...socialLinks, [key]: value };
    setSocialLinks(updatedLinks);
    updateData({ socialLinks: updatedLinks });
  };

  const handleSubmit = async () => {
    try {
      await submitOnboarding();
    } catch (error) {
      console.error('Error submitting onboarding:', error);
      // Handle error (show toast, etc.)
    }
  };

  const handleBack = () => {
    prevStep();
  };

  return (
    <div className="space-y-6">
      {/* Title */}
      <div className="text-center space-y-4">
        <h1 className="text-3xl font-bold text-foreground font-arvo leading-tight">
          Add your social links
        </h1>
        <p className="text-sm text-foreground font-lato leading-relaxed">
          Connect your music and social media profiles to showcase your work and expand your network. (All fields are optional)
        </p>
      </div>

      {/* Social Links Form */}
      <div className="space-y-5">
        {socialFields.map((field) => (
          <div key={field.key} className="space-y-2">
            <Label 
              htmlFor={field.key} 
              className="text-sm font-arvo text-muted-foreground"
            >
              {field.label}
            </Label>
            <Input
              id={field.key}
              type="url"
              value={socialLinks[field.key]}
              onChange={(e) => handleInputChange(field.key, e.target.value)}
              placeholder={field.placeholder}
              className="w-full px-4 py-3 bg-background border border-border rounded text-sm font-lato text-foreground placeholder:text-muted-foreground focus:border-primary focus:ring-1 focus:ring-primary"
            />
          </div>
        ))}
      </div>

      {/* Navigation Buttons */}
      <div className="flex justify-between items-center pt-6">
        <Button
          onClick={handleBack}
          disabled={isLoading}
          variant="outline"
          className="px-6 py-3 font-arvo font-bold text-base border-muted text-muted-foreground hover:bg-muted disabled:opacity-50"
        >
          Back
        </Button>
        <div className="flex items-center gap-5">
          <Button
            onClick={handleSubmit}
            disabled={isLoading}
            className="px-6 py-3 bg-primary text-background font-arvo font-bold text-base hover:bg-primary/90 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
          >
            {isLoading && <Loader2 className="w-4 h-4 animate-spin" />}
            {isLoading ? 'Submitting...' : 'Continue'}
          </Button>
        </div>
      </div>
    </div>
  );
}
